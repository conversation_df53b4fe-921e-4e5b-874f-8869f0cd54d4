import { PrismaClient } from '@prisma/client';
import { formatDuration } from '../lib/utils/duration';

const prisma = new PrismaClient();

async function debugUserRentalsDuration() {
  try {
    console.log('Debugging user rentals duration display...');
    
    // Get user rentals similar to how getUserRentals function works
    const rentals = await prisma.rental.findMany({
      include: {
        product: {
          select: {
            id: true,
            name: true,
            price: true,
            capacity: true,
            imageUrl: true,
            image: true,
            description: true
          }
        },
        payment: {
          select: {
            id: true,
            status: true,
            amount: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5 // Get latest 5 rentals
    });

    console.log(`Found ${rentals.length} rentals:`);
    console.log('================================');
    
    rentals.forEach((rental, index) => {
      console.log(`${index + 1}. Rental ID: ${rental.id.slice(-8)}`);
      console.log(`   Duration (raw): "${rental.duration}"`);
      console.log(`   Duration (type): ${typeof rental.duration}`);
      console.log(`   Duration (formatted): "${formatDuration(rental.duration)}"`);
      console.log(`   Product: ${rental.product?.name}`);
      console.log(`   Status: ${rental.status}`);
      console.log(`   Start Date: ${rental.startDate}`);
      console.log(`   End Date: ${rental.endDate}`);
      console.log('');
    });

    // Check specifically for rentals with null or empty duration
    const rentalsWithNullDuration = await prisma.rental.findMany({
      where: {
        OR: [
          { duration: null },
          { duration: '' }
        ]
      },
      select: {
        id: true,
        duration: true,
        status: true,
        startDate: true,
        endDate: true
      }
    });

    if (rentalsWithNullDuration.length > 0) {
      console.log(`⚠️  Found ${rentalsWithNullDuration.length} rentals with null/empty duration:`);
      rentalsWithNullDuration.forEach(rental => {
        console.log(`   - ID: ${rental.id.slice(-8)}, Duration: "${rental.duration}", Status: ${rental.status}`);
      });
    } else {
      console.log('✅ No rentals with null/empty duration found');
    }

    // Test formatDuration with various inputs
    console.log('\nTesting formatDuration with various inputs:');
    console.log('==========================================');
    const testInputs = [null, '', undefined, '8_JAM', '16_JAM', '1x8_HOURS'];
    testInputs.forEach(input => {
      const result = formatDuration(input as any);
      console.log(`formatDuration(${JSON.stringify(input)}) = "${result}"`);
    });

  } catch (error) {
    console.error('Error debugging user rentals duration:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugUserRentalsDuration();
