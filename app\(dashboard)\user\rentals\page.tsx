import { Metadata } from "next";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { Button } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import { Input } from "@/app/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { LuSearch, LuFilter, LuClock, LuCalendarRange } from "react-icons/lu";
import { getUserRentals } from "@/lib/data/rental";
import { formatCurrency, formatDate } from "@/lib/utils/format";
import { formatDuration } from "@/lib/utils/duration";
import Link from "next/link";
import { ProductImage } from "@/app/components/product/product-image";

export const metadata: Metadata = {
  title: "Rental Saya",
  description: "Daftar penyewaan genset yang Anda lakukan",
};

// Fungsi untuk menentukan variant badge berdasarkan status
function getStatusBadge(status: string) {
  switch (status) {
    case "active":
      return { label: "Aktif", variant: "default" as const };
    case "pending":
      return { label: "Menunggu", variant: "secondary" as const };
    case "completed":
      return { label: "Selesai", variant: "outline" as const };
    case "cancelled":
      return { label: "Dibatalkan", variant: "destructive" as const };
    default:
      return { label: status, variant: "outline" as const };
  }
}

// Tipe data untuk user rentals
type RentalWithRelations = {
  id: string;
  userId: string;
  productId: string;
  startDate: Date;
  endDate: Date;
  status: string;
  amount: number;
  location: string;
  purpose: string;
  arrivalTime: string;
  quantity: number;
  duration?: string | null;
  product: {
    id: string;
    name: string;
    price: number;
    capacity: number;
    imageUrl?: string | null;
    image?: string | null;
    description?: string | null;
  };
  payment: {
    id: string;
    status: string;
    amount: number;
  } | null;
  createdAt: Date;
  updatedAt: Date;
};

export default async function RentalsPage() {
  const session = await auth();
  if (!session?.user) {
    redirect('/login');
  }

  // Fetch user rentals from API or handle error
  let rentals: RentalWithRelations[] = [];
  try {
    rentals = await getUserRentals(session.user.id);
  } catch (error) {
    console.error("Error fetching rentals:", error);
    // Continue with empty rentals array
  }

  return (
    <div className="">
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="absolute right-0 top-0 bottom-0 w-1/3 opacity-10 bg-contain bg-right bg-no-repeat" style={{ backgroundImage: "url('/images/generator.svg')" }}></div>
        <div className="relative">
          <h1 className="text-3xl font-bold tracking-tight text-white">Rental Saya</h1>
          <p className="text-white max-w-xl mt-2">
            Daftar penyewaan genset yang Anda lakukan
          </p>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1 relative">
          <LuSearch className="absolute left-3 top-3 h-4 w-4 text-blue-500 dark:text-blue-400" />
          <Input
            placeholder="Cari berdasarkan nama produk..."
            className="pl-9"
          />
        </div>
        <Button variant="outline" className="flex items-center gap-2 text-violet-500 dark:text-violet-400">
          <LuFilter className="h-4 w-4 text-violet-500 dark:text-violet-400" />
          Filter
        </Button>
      </div>

      {rentals.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="mb-4">Anda belum memiliki rental</p>
            <Link href="/products">
              <Button>Lihat Produk</Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {rentals.map((rental) => {
            // Pastikan semua data tersedia
            if (!rental || !rental.product) {
              return null;
            }

            const statusBadge = getStatusBadge(rental.status);

            return (
              <Card key={rental.id} className="overflow-hidden">
                <div className="md:flex">
                  <div className="md:w-1/4 h-48 md:h-auto overflow-hidden bg-gray-100 relative">
                    <ProductImage
                      imageUrl={rental.product.imageUrl}
                      image={rental.product.image}
                      name={rental.product.name}
                      size="full"
                    />
                  </div>
                  <div className="md:w-3/4">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-xl">{rental.product.name}</CardTitle>
                        <Badge variant={statusBadge.variant}>
                          {statusBadge.label}
                        </Badge>
                      </div>
                      <CardDescription>
                        ID Pemesanan: #{rental.id}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <LuCalendarRange className="h-4 w-4 text-blue-500 dark:text-blue-400" />
                            <span>
                              {formatDate(rental.startDate)} - {formatDate(rental.endDate)}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <LuClock className="h-4 w-4 text-red-500 dark:text-red-400" />
                            <span>Durasi: {formatDuration(rental.duration)}</span>
                          </div>
                          {rental.location && (
                            <div className="flex items-center gap-2">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-violet-500 dark:text-violet-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M12 22s-8-4.5-8-11.8A8 8 0 0 1 12 2a8 8 0 0 1 8 8.2c0 7.3-8 11.8-8 11.8z" />
                                <circle cx="12" cy="10" r="3" />
                              </svg>
                              <span className="truncate">{rental.location}</span>
                            </div>
                          )}
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground mb-1">Total Biaya:</p>
                          <p className="text-xl font-bold text-green-600 dark:text-green-400">
                            {formatCurrency(rental.amount)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-end gap-2">
                      <Link href={`/user/rentals/${rental.id}`}>
                        <Button variant="outline" size="mobile">Detail</Button>
                      </Link>
                      {rental.status === "active" && (
                        <Button variant="destructive" size="mobile">Akhiri Sewa</Button>
                      )}
                      {rental.status === "pending" && rental.payment?.status === "DEPOSIT_PENDING" && (
                        <Link href={`/user/payments/deposit/${rental.id}`}>
                          <Button variant="success" size="mobile">Bayar Sekarang</Button>
                        </Link>
                      )}
                      {rental.status === "completed" && (
                        <Link href="/user/catalog">
                          <Button variant="outline" size="mobile">Sewa Lagi</Button>
                        </Link>
                      )}
                    </CardFooter>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}
