import { formatDuration } from '../lib/utils/duration';

// Test data yang menyimulasikan rental dari getUserRentals
const testRentals = [
  { 
    id: 'cmbhx12180001tm60i3qr9yrb', 
    duration: '8_JAM',
    product: { name: 'Genset 2' }
  },
  { 
    id: 'test2', 
    duration: '16_JAM',
    product: { name: 'Genset 1' }
  },
  { 
    id: 'test3', 
    duration: null,
    product: { name: 'Genset 3' }
  },
  { 
    id: 'test4', 
    duration: undefined,
    product: { name: 'Genset 4' }
  },
  { 
    id: 'test5', 
    duration: '',
    product: { name: 'Genset 5' }
  }
];

console.log('Testing user rentals duration display:');
console.log('=====================================');

testRentals.forEach((rental, index) => {
  const formattedDuration = formatDuration(rental.duration as any);
  console.log(`${index + 1}. Rental ${rental.id.slice(-8)}: "${rental.duration}" -> "Durasi: ${formattedDuration}"`);
  console.log(`   Product: ${rental.product.name}`);
  console.log('');
});

console.log('Expected behavior:');
console.log('- "8_JAM" should show "Durasi: 8 Jam"');
console.log('- "16_JAM" should show "Durasi: 16 Jam"');
console.log('- null should show "Durasi: -"');
console.log('- undefined should show "Durasi: -"');
console.log('- empty string should show "Durasi: -"');

console.log('\nIf user/rentals page still shows "Durasi: -":');
console.log('1. Check if duration field is included in getUserRentals query');
console.log('2. Check if duration field exists in database');
console.log('3. Check if type definition includes duration field');
console.log('4. Hard refresh browser (Ctrl+F5)');
